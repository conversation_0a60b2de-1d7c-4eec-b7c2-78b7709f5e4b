{"name": "web3game", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@react-three/fiber": "^9.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "gsap": "^3.13.0", "lucide-react": "^0.509.0", "motion": "^12.10.4", "next": "15.3.2", "ogl": "^1.0.11", "openai": "^4.98.0", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "tailwind-merge": "^3.2.0", "three": "^0.176.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.176.0", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4.1.5", "tw-animate-css": "^1.2.9", "typescript": "^5"}}