"use client";
import { MobileWarningModal } from "@/components/MobileWarningModal";
import { useIsMobile } from "@/hooks/useIsMobile";
import { cn } from "@/lib/utils";
import { useRouter } from "next/navigation";
import React from "react";
import { VscShield } from "react-icons/vsc";
import { CreatorBanner } from "@/components/creator-banner";

export const GamesGrid = ({
  className,
  children,
}: {
  className?: string;
  children?: React.ReactNode;
}) => {
  return (
    <div className="py-10 flex flex-col items-center justify-center w-full relative min-h-screen bg-zinc-800">
      <div className="relative z-10 w-full">
        <h1 className="text-4xl font-bold mb-8 text-center">
          ZeroTrust Games by Opsek
        </h1>
        <div
          className={cn(
            "mx-auto grid max-w-7xl grid-cols-1 gap-4 md:auto-rows-[22rem] md:grid-cols-3 px-4",
            className
          )}
        >
          {children}
        </div>
        <CreatorBanner className="mt-8" />
      </div>
    </div>
  );
};

export const GamesGridItem = ({
  className,
  title,
  description,
  header,
  icon,
  active = false,
  onClick,
  colors = [[0, 255, 255]],
}: {
  className?: string;
  title?: string | React.ReactNode;
  description?: string | React.ReactNode;
  header?: React.ReactNode;
  icon?: React.ReactNode;
  active?: boolean;
  onClick?: () => void;
  colors?: number[][];
}) => {
  const [hovered, setHovered] = React.useState(false);

  const getBackgroundGradient = () => {
    if (colors.length === 0) {
      return {
        background: "linear-gradient(135deg, #111 0%, #000 100%)",
      };
    }

    const colorStart = `rgb(${colors[0][0]}, ${colors[0][1]}, ${colors[0][2]})`;
    const colorEnd =
      colors.length > 1
        ? `rgb(${colors[1][0]}, ${colors[1][1]}, ${colors[1][2]})`
        : "rgb(0, 0, 0)";

    return {
      background: `linear-gradient(135deg, ${colorStart}22 0%, ${colorEnd}55 100%)`,
      boxShadow: hovered ? `0 0 20px 0 ${colorStart}33` : "none",
    };
  };

  return (
    <div
      className={cn(
        "group/bento row-span-1 flex flex-col justify-between space-y-4 rounded-xl border border-white/10 bg-black/40 backdrop-blur-sm p-4 transition-all duration-300 hover:shadow-xl hover:bg-black/60 relative overflow-hidden",
        active ? "cursor-pointer" : "opacity-70 cursor-not-allowed",
        className
      )}
      style={getBackgroundGradient()}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      onClick={active ? onClick : undefined}
    >
      {header}

      <div className="transition duration-200 group-hover/bento:translate-x-2 relative z-10 flex flex-col h-full justify-center">
        {icon || <VscShield size={60} />}
        <div className="mt-4 mb-3 font-sans font-bold text-white group-hover/bento:text-white text-3xl">
          {title}
        </div>
        <div className="font-sans text-lg font-normal text-white/80 group-hover/bento:text-white/90">
          {description}
        </div>
        {!active && (
          <div className="absolute top-0 right-0 mt-2 mr-2">
            <span className="bg-black/70 text-white px-3 py-1 rounded-md text-sm font-medium">
              Coming Soon
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export function GamesGridContent() {
  const router = useRouter();
  const isMobile = useIsMobile();

  const handleGameClick = (id: number) => {
    router.push(`/game/${id}`);
  };

  if (isMobile) {
    return <MobileWarningModal isOpen={isMobile} />;
  }

  return (
    <GamesGrid>
      <GamesGridItem
        title="Job Offer"
        description="Welcome to 2025, welcome to the real world… Some things are too good and too real to be true. 👀"
        active={!isMobile}
        onClick={() => handleGameClick(1)}
        colors={[
          [236, 72, 153],
          [232, 121, 249],
        ]}
      />

      <GamesGridItem
        title="Fatal Trust"
        description="Stay tuned…"
        active={false}
        colors={[[125, 211, 252]]}
      />

      <GamesGridItem
        title="Fatal Trust 2"
        description="Stay tuned…"
        active={false}
        colors={[[74, 222, 128]]}
      />
    </GamesGrid>
  );
}
