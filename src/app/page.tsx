"use client";

import AppPreLoader from "@/app/AppPreloader";
import { GamesGridContent } from "./GamesGrid";
import { useState, useEffect } from "react";

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);
  const [hasSeenPreloader, setHasSeenPreloader] = useState(false);

  useEffect(() => {
    if (hasSeenPreloader) {
      setIsLoading(false);
    } else {
      const timer = setTimeout(() => {
        setIsLoading(false);
        setHasSeenPreloader(true);
      }, 6000);

      return () => clearTimeout(timer);
    }
  }, [hasSeenPreloader]);

  return (
    <>
      {isLoading ? (
        <AppPreLoader />
      ) : (
        <div className="min-h-screen bg-black text-white">
          <GamesGridContent />
        </div>
      )}
    </>
  );
}
