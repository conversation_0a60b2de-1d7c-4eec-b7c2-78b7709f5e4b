"use client";

import { useEffect, useState } from "react";

interface SimpleLoadingProps {
  onLoadingComplete?: () => void;
  loadingTime?: number;
  title?: string;
}

export default function SimpleLoading({
  onLoadingComplete,
  loadingTime = 3500,
  title = "ZeroTrust",
}: SimpleLoadingProps) {
  const [progress, setProgress] = useState(0);
  const [showContent, setShowContent] = useState(true);
  const [cursorVisible, setCursorVisible] = useState(true);

  useEffect(() => {
    const cursorInterval = setInterval(() => {
      setCursorVisible((prev) => !prev);
    }, 500);

    return () => clearInterval(cursorInterval);
  }, []);

  useEffect(() => {
    const startTime = Date.now();
    const interval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const newProgress = Math.min(100, (elapsed / loadingTime) * 100);
      setProgress(newProgress);

      if (newProgress >= 100) {
        clearInterval(interval);
        // Fade out animation
        setTimeout(() => {
          setShowContent(false);
          // Call the callback after fade out animation completes
          setTimeout(() => {
            if (onLoadingComplete) onLoadingComplete();
          }, 500);
        }, 200);
      }
    }, 50);

    return () => clearInterval(interval);
  }, [loadingTime, onLoadingComplete]);

  if (!showContent) return null;

  return (
    <div className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-black transition-opacity duration-500">
      <div className="text-center mb-12">
        <h1 className="text-5xl font-bold text-white mb-2">{title}</h1>
        <div className="text-2xl text-gray-400 flex items-center justify-center">
          <span>Interactive Security Awareness Game</span>
          <span
            className={`ml-1 ${cursorVisible ? "opacity-100" : "opacity-0"}`}
          >
            |
          </span>
        </div>
      </div>

      <div className="w-full max-w-md px-8">
        <div className="w-full bg-gray-800 rounded-full h-3 mb-4">
          <div
            className="bg-blue-600 h-3 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${progress}%` }}
          ></div>
        </div>

        <div className="text-white text-sm text-center">
          Loading... {Math.round(progress)}%
        </div>
      </div>
    </div>
  );
}
