"use client";

import { useParams } from "next/navigation";
import Link from "next/link";
import { MobileWarningModal } from "@/components/MobileWarningModal";
import { useIsMobile } from "@/hooks/useIsMobile";
import { FirstGame } from "./first-game/FirstGame";
import { Suspense, useState } from "react";
import SimpleLoading from "./loading-screens/simple-loading";

// Map of game IDs to their respective components and loading screens
interface GameConfig {
  game: React.ComponentType;
  loadingScreen: React.ComponentType<{ onLoadingComplete?: () => void }>;
}

const GAMES_MAP: Record<string, GameConfig> = {
  "1": {
    game: FirstGame,
    loadingScreen: SimpleLoading,
  },
  // Add more games here as they are developed
};

export default function GamePage() {
  const params = useParams();
  const gameId = params.id as string;
  const isMobile = useIsMobile();
  const [isLoading, setIsLoading] = useState(true);

  // Get the game config based on the ID
  const gameConfig = GAMES_MAP[gameId];

  // If no game is found for this ID
  if (!gameConfig) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-black text-white p-4">
        <h1 className="text-4xl font-bold mb-6">Game Not Found</h1>
        <p className="text-xl mb-8">
          Sorry, the game with ID {gameId} does not exist.
          <Link
            href="/"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Back to Home
          </Link>
        </p>
      </div>
    );
  }

  // Show mobile warning if on mobile device
  if (isMobile) {
    return <MobileWarningModal isOpen={true} />;
  }

  // Get the game and loading screen components
  const GameComponent = gameConfig.game;
  const LoadingScreenComponent = gameConfig.loadingScreen;

  return (
    <div className="min-h-screen bg-black">
      {isLoading ? (
        <LoadingScreenComponent onLoadingComplete={() => setIsLoading(false)} />
      ) : (
        <Suspense fallback={<GameLoading />}>
          <GameComponent />
        </Suspense>
      )}
      <MobileWarningModal autoDetect={true} />
    </div>
  );
}

// Loading component while game is initializing
function GameLoading() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-black text-white">
      <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
      <p className="mt-4 text-xl">Loading game...</p>
    </div>
  );
}
