export interface ChatMessage {
  id: string;
  type: 'question' | 'user-answer' | 'result' | 'hint' | 'loading' | 'final-score';
  content: any;
  timestamp: Date;
}

export interface QuestionMessageContent {
  questionId: number;
  questionNumber: number;
  question: string;
  maxPoints: number;
  expectedAnswersCount: number;
  totalQuestions: number;
}

export interface UserAnswerContent {
  questionId: number;
  answer: string;
}

export interface ResultMessageContent {
  questionId: number;
  question: string;
  totalPoints: number;
  maxPoints: number;
  correctAnswers: string[];
  expectedAnswersCount: number;
  isLastQuestion: boolean;
}

export interface HintMessageContent {
  questionId: number;
  hint: string;
  points: number;
}

export interface FinalScoreContent {
  finalScore: number;
  totalMaxPoints: number;
  questionResults: ResultMessageContent[];
}
