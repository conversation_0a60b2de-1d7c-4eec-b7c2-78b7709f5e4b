import React from "react";

interface ProgressIndicatorProps {
  currentQuestion: number;
  totalQuestions: number;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  currentQuestion,
  totalQuestions,
}) => {
  const progressPercentage = (currentQuestion / totalQuestions) * 100;

  return (
    <div className="mb-6 p-4 bg-[#40414f] rounded-lg">
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm text-gray-300">Progress</span>
        <span className="text-sm text-gray-300">
          Question {currentQuestion} of {totalQuestions}
        </span>
      </div>
      <div className="w-full bg-gray-700 rounded-full h-2">
        <div
          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
          style={{ width: `${progressPercentage}%` }}
        ></div>
      </div>
    </div>
  );
};
