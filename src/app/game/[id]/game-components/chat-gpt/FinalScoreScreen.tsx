import React, { useState, useEffect } from "react";
import { Share2, <PERSON>, EyeOff } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ChatHeader } from "./ChatHeader";
import { FinalScoreContent } from "./types";

interface FinalScoreScreenProps {
  content: FinalScoreContent;
}

export const FinalScoreScreen: React.FC<FinalScoreScreenProps> = ({
  content,
}) => {
  const { finalScore, totalMaxPoints, questionResults } = content;
  const [showAllAnswers, setShowAllAnswers] = useState(false);
  const [allExpectedAnswers, setAllExpectedAnswers] = useState<
    Record<number, string[]>
  >({});
  const [isLoadingAnswers, setIsLoadingAnswers] = useState(false);

  useEffect(() => {
    const fetchAllAnswers = async () => {
      if (showAllAnswers && Object.keys(allExpectedAnswers).length === 0) {
        setIsLoadingAnswers(true);
        try {
          const response = await fetch("/api/answers/1");
          if (response.ok) {
            const answers = await response.json();
            setAllExpectedAnswers(answers);
          }
        } catch (error) {
          console.error("Failed to fetch expected answers:", error);
        } finally {
          setIsLoadingAnswers(false);
        }
      }
    };

    fetchAllAnswers();
  }, [showAllAnswers, allExpectedAnswers]);

  const handleShare = () => {
    const text = `I scored ${finalScore}% on the Zero Trust Game!\n\nPlay this interactive experience to detect social engineering attacks: https://zero-trust-game.xyz\nBy @theSouilos & @0xmrudenko`;
    window.open(
      `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`,
      "_blank"
    );
  };

  return (
    <div className="flex flex-col overflow-hidden border border-gray-700 shadow-xl h-full bg-[#343541]">
      <ChatHeader />
      <div className="flex-1 overflow-y-auto p-6 text-white">
        <div className="text-center mb-6">
          <h2 className="text-3xl font-bold mb-4">🎉 Game Completed!</h2>
          <div className="text-5xl font-bold text-green-400 mb-2">
            {finalScore}
          </div>
          <div className="text-xl text-gray-300">
            out of {totalMaxPoints} points
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold">📊 Your Results:</h3>
            <Button
              onClick={() => setShowAllAnswers(!showAllAnswers)}
              variant="outline"
              size="sm"
              disabled={isLoadingAnswers}
              className="bg-blue-600/20 border-blue-600/50 text-blue-400 hover:bg-blue-600/30 cursor-pointer"
            >
              {isLoadingAnswers ? (
                "Loading..."
              ) : showAllAnswers ? (
                <>
                  <EyeOff className="w-4 h-4 mr-1" />
                  Hide All Answers
                </>
              ) : (
                <>
                  <Eye className="w-4 h-4 mr-1" />
                  Reveal All Answers
                </>
              )}
            </Button>
          </div>
          {questionResults.map((result, index) => {
            const expectedAnswers = allExpectedAnswers[result.questionId] || [];
            const missedAnswers = expectedAnswers.filter(
              (answer) => !result.correctAnswers.includes(answer)
            );

            return (
              <div
                key={result.questionId}
                className="bg-[#40414f] rounded-lg p-4"
              >
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-semibold">Question {index + 1}</h4>
                  <span className="text-green-400 font-bold">
                    {result.totalPoints}/{result.maxPoints} pts
                  </span>
                </div>
                <p className="text-gray-300 text-sm mb-3">{result.question}</p>

                {result.correctAnswers.length > 0 && (
                  <div className="mb-3">
                    <p className="text-green-400 text-sm font-medium mb-1">
                      ✅ You identified:
                    </p>
                    <ul className="text-sm text-gray-300 space-y-1">
                      {result.correctAnswers.map((answer) => (
                        <li key={answer} className="pl-2">
                          • {answer}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {showAllAnswers && missedAnswers.length > 0 && (
                  <div>
                    <p className="text-orange-400 text-sm font-medium mb-1">
                      ❌ You missed:
                    </p>
                    <ul className="text-sm text-gray-400 space-y-1">
                      {missedAnswers.map((answer) => (
                        <li key={answer} className="pl-2">
                          • {answer}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {finalScore >= 85 && (
          <div className="mt-6 text-center">
            <Button
              onClick={handleShare}
              className="bg-[#1d9bf0] hover:bg-[#1a8cd8] text-white cursor-pointer"
            >
              <Share2 className="w-4 h-4 mr-2" />
              Share on Twitter
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
