import React from "react";
import {
  ChevronRight,
  Download,
  Layers,
  Sparkles,
} from "lucide-react";

type IconButtonProps = {
  icon: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
};

const IconButton = ({
  icon,
  onClick,
  disabled,
  className,
}: IconButtonProps) => (
  <button
    type="button"
    onClick={onClick}
    disabled={disabled}
    className={`p-1 rounded ${
      disabled ? "cursor-not-allowed" : "hover:bg-gray-700"
    } ${className || ""}`}
  >
    {icon}
  </button>
);

const HeaderIcons = ({ icons }: { icons: React.ReactNode[] }) => (
  <div className="flex space-x-3 text-gray-400">
    {icons.map((icon, index) => {
      const iconType = (icon as any)?.type?.name || `icon-${index}`;
      return <IconButton key={`header-${iconType}`} icon={icon} />;
    })}
  </div>
);

export const ChatHeader = () => {
  return (
    <div className="flex items-center px-3 py-2 bg-[#1e1e1e] border-b border-gray-700">
      <HeaderIcons
        icons={[
          <Layers size={18} key="layers" />,
          <Sparkles size={18} key="sp" />,
        ]}
      />
      <div className="flex items-center text-white font-medium ml-4">
        ChatGPT
        <ChevronRight size={20} className="ml-1" />
      </div>
      <div className="ml-auto">
        <HeaderIcons
          icons={[
            <Download size={18} key="download" />,
            <Layers size={18} key="layers" />,
          ]}
        />
      </div>
    </div>
  );
};
