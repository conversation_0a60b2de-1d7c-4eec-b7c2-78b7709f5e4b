import React from "react";
import { Check<PERSON>ircle2, Help<PERSON><PERSON><PERSON>, ArrowR<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ChatMessage as ChatMessageType, QuestionMessageContent, UserAnswerContent, ResultMessageContent, HintMessageContent } from "./types";

interface ChatMessageProps {
  message: ChatMessageType;
  onNextQuestion?: () => void;
}

const QuestionMessage: React.FC<{ content: QuestionMessageContent }> = ({ content }) => (
  <div className="mb-4">
    <div className="font-semibold mb-2">ChatGPT:</div>
    <div className="pl-2">
      <div className="prose prose-invert prose-sm max-w-none">
        <h3 className="text-lg font-semibold text-blue-400 mb-3">
          Question {content.questionNumber}
        </h3>
        <p className="text-white mb-4">{content.question}</p>
        <p className="text-sm text-gray-400">
          Expected answers: {content.expectedAnswersCount} | 
          Max points: {content.maxPoints}
        </p>
      </div>
    </div>
  </div>
);

const UserAnswerMessage: React.FC<{ content: UserAnswerContent }> = ({ content }) => (
  <div className="mb-4">
    <div className="font-semibold mb-2">You:</div>
    <div className="pl-2">
      <div className="bg-[#40414f] rounded-lg p-3 text-white">
        {content.answer}
      </div>
    </div>
  </div>
);

const ResultMessage: React.FC<{ content: ResultMessageContent; onNextQuestion?: () => void }> = ({ 
  content, 
  onNextQuestion 
}) => (
  <div className="mb-4">
    <div className="font-semibold mb-2">ChatGPT:</div>
    <div className="pl-2">
      <div className="bg-[#40414f] rounded-lg p-4">
        <div className="flex items-center gap-2 mb-3">
          <CheckCircle2 className="w-5 h-5 text-green-400" />
          <span className="font-semibold text-green-400">
            You scored {content.totalPoints} out of {content.maxPoints} points!
          </span>
        </div>
        
        {content.correctAnswers.length > 0 && (
          <div>
            <p className="font-medium text-green-400 mb-2">
              ✅ Correctly identified:
            </p>
            <ul className="space-y-1 text-sm">
              {content.correctAnswers.map((answer) => (
                <li key={answer} className="flex items-start gap-2">
                  <span className="text-green-400 mt-1">•</span>
                  <span>{answer}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {!content.isLastQuestion && onNextQuestion && (
          <div className="mt-4 pt-3 border-t border-gray-600">
            <Button
              onClick={onNextQuestion}
              className="bg-blue-600 hover:bg-blue-700 text-white cursor-pointer"
            >
              Next Question
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        )}
      </div>
    </div>
  </div>
);

const HintMessage: React.FC<{ content: HintMessageContent }> = ({ content }) => (
  <div className="mb-4 p-3 bg-yellow-900/30 border border-yellow-600/50 rounded-lg">
    <div className="flex items-start gap-2">
      <HelpCircle className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
      <div>
        <div className="font-semibold text-yellow-400 mb-1">Hint:</div>
        <div className="text-yellow-100">{content.hint}</div>
      </div>
    </div>
  </div>
);

const LoadingMessage = () => {
  const [dots, setDots] = React.useState("");

  React.useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => (prev.length >= 3 ? "" : prev + "."));
    }, 400);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="mb-4">
      <div className="font-semibold mb-2">ChatGPT:</div>
      <div className="pl-2">
        <div className="prose prose-invert prose-sm max-w-none">
          <p className="flex items-center">
            Evaluating your answer
            <span className="inline-flex ml-1 min-w-[30px] font-bold text-xl leading-tight tracking-wider">
              {dots}
            </span>
          </p>
        </div>
      </div>
    </div>
  );
};

export const ChatMessageComponent: React.FC<ChatMessageProps> = ({ message, onNextQuestion }) => {
  switch (message.type) {
    case 'question':
      return <QuestionMessage content={message.content} />;
    case 'user-answer':
      return <UserAnswerMessage content={message.content} />;
    case 'result':
      return <ResultMessage content={message.content} onNextQuestion={onNextQuestion} />;
    case 'hint':
      return <HintMessage content={message.content} />;
    case 'loading':
      return <LoadingMessage />;
    default:
      return null;
  }
};
