"use client";

import React from "react";
import {
  ChevronDown,
  Co<PERSON>,
  MoreVertical,
  Eye,
  ExternalLink,
} from "lucide-react";
import { BaseFrame } from "./common/base-frame";

interface Token {
  symbol: string;
  name: string;
  balance: number;
  usdValue: number;
  icon?: string;
  percentChange?: number;
}

interface MetamaskFrameProps {
  balances: Token[];
  totalBalance?: number;
  accountAddress?: string;
  accountName?: string;
  networkName?: string;
  percentChange?: number;
}

export default function MetamaskFrame({
  balances = [],
  totalBalance = 0,
  accountAddress = "0xAfcC3...6901b",
  accountName = "Account 1",
  networkName = "Ethereum Mainnet",
  percentChange = 7.95,
}: MetamaskFrameProps) {
  const calculatedTotalBalance =
    totalBalance || balances.reduce((sum, token) => sum + token.usdValue, 0);

  const changeAmount = (calculatedTotalBalance * percentChange) / 100;

  const formattedTotalBalance = calculatedTotalBalance.toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  const formattedChangeAmount = Math.abs(changeAmount).toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  const accountHeader = (
    <div className="p-4 border-b border-gray-800">
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center mr-2">
            <span className="text-white text-xs">Ξ</span>
          </div>
          <div className="flex items-center">
            <span className="font-semibold mr-1">{accountName}</span>
            <ChevronDown size={16} />
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 rounded-full bg-gray-800 flex items-center justify-center">
            <span className="text-white text-xs">🌐</span>
          </div>
          <MoreVertical size={20} />
        </div>
      </div>

      <div className="flex items-center justify-between text-sm text-gray-400 mb-2">
        <div className="flex items-center">
          <span>{accountAddress}</span>
          <Copy size={14} className="ml-1 cursor-pointer" />
        </div>
      </div>
    </div>
  );

  const balanceSection = (
    <div className="p-4 border-b border-gray-800">
      <div className="flex items-center justify-between">
        <div className="text-3xl font-bold">${formattedTotalBalance}</div>
        <Eye size={20} className="text-gray-400" />
      </div>

      <div className="flex items-center mt-1">
        <span
          className={`${
            percentChange >= 0 ? "text-green-500" : "text-red-500"
          }`}
        >
          {percentChange >= 0 ? "+" : "-"}${formattedChangeAmount} (
          {Math.abs(percentChange)}%)
        </span>
        <div className="ml-2 text-blue-500 flex items-center">
          <span>Portfolio</span>
          <ExternalLink size={14} className="ml-1" />
        </div>
      </div>
    </div>
  );

  const actionButtons = (
    <div className="flex justify-between p-4 border-b border-gray-800">
      <div className="flex flex-col items-center">
        <div className="w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center mb-1">
          <span className="text-xl">↔</span>
        </div>
        <span className="text-xs">Buy & Sell</span>
      </div>

      <div className="flex flex-col items-center">
        <div className="w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center mb-1">
          <span className="text-xl">⇄</span>
        </div>
        <span className="text-xs">Swap</span>
      </div>

      <div className="flex flex-col items-center">
        <div className="w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center mb-1">
          <span className="text-xl">⟳</span>
        </div>
        <span className="text-xs">Bridge</span>
      </div>

      <div className="flex flex-col items-center">
        <div className="w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center mb-1">
          <span className="text-xl">↗</span>
        </div>
        <span className="text-xs">Send</span>
      </div>

      <div className="flex flex-col items-center">
        <div className="w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center mb-1">
          <span className="text-xl">⊞</span>
        </div>
        <span className="text-xs">Receive</span>
      </div>
    </div>
  );

  const tabsSection = (
    <div className="flex border-b border-gray-800">
      <div className="flex-1 text-center py-3 border-b-2 border-blue-500 text-blue-500">
        Tokens
      </div>
      <div className="flex-1 text-center py-3 text-gray-400">NFTs</div>
      <div className="flex-1 text-center py-3 text-gray-400">Activity</div>
    </div>
  );

  const networkSection = (
    <div className="p-3 flex justify-between items-center border-b border-gray-800">
      <div className="flex items-center">
        <span>{networkName}</span>
        <ChevronDown size={16} className="ml-1" />
      </div>
      <div className="flex space-x-2">
        <div className="w-6 h-6 flex items-center justify-center">
          <span>≡</span>
        </div>
        <div className="w-6 h-6 flex items-center justify-center">
          <MoreVertical size={16} />
        </div>
      </div>
    </div>
  );

  const tokensList = (
    <div className="flex-1 overflow-y-auto">
      {balances.map((token) => (
        <div
          key={`${token.symbol}-${token.name}`}
          className="p-3 border-b border-gray-800 flex items-center justify-between"
        >
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center mr-3 relative">
              {token.icon ? (
                <img
                  src={`/${token.icon}`}
                  alt={token.symbol}
                  className="w-8 h-8 rounded-full"
                  onError={(e) => {
                    e.currentTarget.src = `https://cryptologos.cc/logos/${token.name.toLowerCase()}-${token.symbol.toLowerCase()}-logo.png`;
                  }}
                />
              ) : (
                <span className="text-sm font-bold">
                  {token.symbol.substring(0, 2)}
                </span>
              )}
              <div className="absolute bottom-0 right-0 w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white text-[8px]">Ξ</span>
              </div>
            </div>
            <div>
              <div className="font-medium flex items-center">
                {token.name}
                {token.name.toLowerCase() === "ethereum" && (
                  <span className="ml-2 text-blue-500 text-sm">• Stake</span>
                )}
              </div>
              <div
                className={`text-sm ${
                  token.percentChange && token.percentChange >= 0
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {token.percentChange
                  ? (token.percentChange >= 0 ? "+" : "") +
                    token.percentChange +
                    "%"
                  : ""}
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="font-medium">${token.usdValue.toFixed(2)}</div>
            <div className="text-sm text-gray-400">
              {token.balance.toFixed(token.balance < 1 ? 5 : 2)} {token.symbol}
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const content = (
    <>
      {balanceSection}
      {actionButtons}
      {tabsSection}
      {networkSection}
      {tokensList}
    </>
  );

  return (
    <BaseFrame
      headerContent={accountHeader}
      backgroundColor="bg-black"
      textColor="text-white"
      borderColor="border-gray-800"
      fullHeight
    >
      {content}
    </BaseFrame>
  );
}
