import type React from "react";
import { cn } from "@/lib/utils";
import { BaseFrame } from "./common/base-frame";

interface NoteFrameProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  timestamp?: string;
  instructions?: React.ReactNode;
  children?: React.ReactNode;
}

export function NoteFrame({
  title,
  timestamp,
  instructions,
  children,
  className,
  ...props
}: NoteFrameProps) {
  const currentTimestamp =
    timestamp ||
    new Date().toLocaleString("en-GB", {
      day: "numeric",
      month: "long",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });

  const headerContent = (
    <div className="p-4 text-right text-sm text-zinc-400">
      {currentTimestamp}
    </div>
  );

  const content = (
    <div className={cn("p-6", className)} {...props}>
      <h1 className="mb-2 text-3xl font-semibold text-zinc-100">{title}</h1>

      {instructions && <div className="mb-4 text-zinc-300">{instructions}</div>}

      <div className="text-zinc-300">{children}</div>
    </div>
  );

  return (
    <BaseFrame
      headerContent={headerContent}
      backgroundColor="bg-zinc-900"
      textColor="text-zinc-200"
      borderColor="border-zinc-700"
      fullHeight
    >
      {content}
    </BaseFrame>
  );
}
