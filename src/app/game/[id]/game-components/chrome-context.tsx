"use client";

import React, {
  createContext,
  useContext,
  useState,
  useMemo,
  ReactNode,
  useCallback,
} from "react";

interface Tab {
  name: string;
  url: string;
  content: React.ReactNode;
  closable?: boolean;
}

interface Extension {
  id: string;
  name: string;
  icon: string;
  content: React.ReactNode;
  active: boolean;
}

interface ChromeContextType {
  tabs: Tab[];
  activeTabIndex: number;
  setActiveTabIndex: (index: number) => void;
  addTab: (tab: Tab) => void;
  closeTab: (index: number) => void;

  extensions: Extension[];
  activeExtension: string | null;
  toggleExtension: (extensionId: string) => void;

  isTabOpen: (tabName: string) => boolean;
  openTabByName: (tabName: string) => void;
}

const ChromeContext = createContext<ChromeContextType | undefined>(undefined);

export function ChromeProvider({
  children,
  initialTabs = [],
  initialExtensions: extensions = [],
}: {
  children: ReactNode;
  initialTabs?: Tab[];
  initialExtensions?: Extension[];
}) {
  const [tabs, setTabs] = useState<Tab[]>(initialTabs);
  const [activeTabIndex, setActiveTabIndex] = useState(0);

  const [activeExtension, setActiveExtension] = useState<string | null>(null);

  const addTab = useCallback(
    (tab: Tab) => {
      setTabs((prevTabs) => [...prevTabs, tab]);
      setActiveTabIndex(tabs.length);
    },
    [tabs.length]
  );

  const closeTab = useCallback(
    (index: number) => {
      if (tabs.length <= 1) return;

      setTabs((prevTabs) => {
        const newTabs = [...prevTabs];
        newTabs.splice(index, 1);
        return newTabs;
      });

      if (index === activeTabIndex) {
        setActiveTabIndex(index === 0 ? 0 : index - 1);
      } else if (index < activeTabIndex) {
        setActiveTabIndex(activeTabIndex - 1);
      }
    },
    [tabs.length, activeTabIndex]
  );

  const toggleExtension = useCallback((extensionId: string) => {
    setActiveExtension((prev) => (prev === extensionId ? null : extensionId));
  }, []);

  const isTabOpen = useCallback(
    (tabName: string) => {
      return tabs.some((tab) => tab.name === tabName);
    },
    [tabs]
  );

  const openTabByName = useCallback(
    (tabName: string) => {
      const tabIndex = tabs.findIndex((tab) => tab.name === tabName);
      if (tabIndex !== -1) {
        setActiveTabIndex(tabIndex);
      }
    },
    [tabs]
  );

  const value = useMemo(
    () => ({
      tabs,
      activeTabIndex,
      setActiveTabIndex,
      addTab,
      closeTab,

      extensions,
      activeExtension,
      toggleExtension,

      isTabOpen,
      openTabByName,
    }),
    [
      tabs,
      activeTabIndex,
      setActiveTabIndex,
      addTab,
      closeTab,
      extensions,
      activeExtension,
      toggleExtension,
      isTabOpen,
      openTabByName,
    ]
  );

  return (
    <ChromeContext.Provider value={value}>{children}</ChromeContext.Provider>
  );
}

export function useChrome() {
  const context = useContext(ChromeContext);

  if (context === undefined) {
    throw new Error("useChrome must be used within a ChromeProvider");
  }

  return context;
}
