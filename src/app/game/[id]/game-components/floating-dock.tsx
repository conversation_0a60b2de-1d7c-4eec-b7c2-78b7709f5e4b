"use client";

import { cn } from "@/lib/utils";
import {
  MotionValue,
  motion,
  useMotionValue,
  useSpring,
  useTransform,
} from "motion/react";
import { useRef } from "react";
import { useMacOS } from "./macos-context";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useGameReset } from "../first-game/first-game.context";
import { createDockItem, DockItem } from "./common/dock-item-creator";

export const FloatingDock = () => {
  const { openWindow, closeWindow, isWindowOpen, isWindowMinimized, windows } =
    useMacOS();
  const { restartGame } = useGameReset();

  const activeApps: DockItem[] = windows.map((window) => ({
    icon: window.icon,
    label: window.title,
    id: window.id,
    isActive: isWindowOpen(window.id) || isWindowMinimized(window.id),
    isMinimized: isWindowMinimized(window.id),
    isClickable: true,
    onClick: () => openWindow(window.id),
  }));

  const additionalAppsData = [
    {
      id: "app-store",
      label: "App Store",
      iconSrc: "/dock-icons/app-store.jpg",
    },
    { id: "music", label: "Music", iconSrc: "/dock-icons/apple-music.png" },
    { id: "slack", label: "Slack", iconSrc: "/dock-icons/slack.png" },
    { id: "vscode", label: "VS Code", iconSrc: "/dock-icons/vs-code.png" },
  ];

  const additionalApps: DockItem[] = additionalAppsData.map((app) =>
    createDockItem(app)
  );

  const restartIcon = (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="h-full w-full text-green-500 rounded-full"
    >
      <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
      <path d="M3 3v5h5" />
    </svg>
  );

  const restartItem = createDockItem({
    id: "restart",
    label: "Restart Game",
    icon: restartIcon,
    isClickable: true,
    onClick: () => {
      restartGame();

      const openWindowIds = windows
        .filter((window) => window.id !== "notes" && isWindowOpen(window.id))
        .map((window) => window.id);

      openWindowIds.forEach((windowId) => {
        closeWindow(windowId);
      });

      openWindow("notes");
    },
  });

  const quitIcon = (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="h-full w-full text-red-500 rounded-full"
    >
      <path d="M18 6L6 18M6 6l12 12" />
    </svg>
  );

  const quitItem = createDockItem({
    id: "quit",
    label: "Quit Game",
    icon: quitIcon,
    isClickable: true,
    onClick: () => (window.location.href = "/"),
  });

  const dockItems = [...activeApps, ...additionalApps, restartItem, quitItem];

  return <FloatingDockDesktop items={dockItems} />;
};

const FloatingDockDesktop = ({ items }: { items: DockItem[] }) => {
  const mouseX = useMotionValue(Infinity);
  return (
    <motion.div
      onMouseMove={(e) => mouseX.set(e.pageX)}
      onMouseLeave={() => mouseX.set(Infinity)}
      className="fixed bottom-4 left-1/2 z-50 -translate-x-1/2 flex h-16 items-end gap-4 rounded-2xl bg-white/20 px-4 pb-3 backdrop-blur-lg"
    >
      {items.map((item) => (
        <IconContainer mouseX={mouseX} key={item.id} {...item} />
      ))}
    </motion.div>
  );
};

function IconContainer({
  mouseX,
  label,
  icon,
  isActive,
  isMinimized,
  isClickable = false,
  onClick,
}: DockItem & {
  mouseX: MotionValue;
}) {
  const ref = useRef<HTMLDivElement>(null);

  const distance = useTransform(mouseX, (val) => {
    const bounds = ref.current?.getBoundingClientRect() ?? { x: 0, width: 0 };
    return val - bounds.x - bounds.width / 2;
  });

  const widthTransform = useTransform(distance, [-150, 0, 150], [40, 80, 40]);
  const heightTransform = useTransform(distance, [-150, 0, 150], [40, 80, 40]);

  const width = useSpring(widthTransform, {
    mass: 0.1,
    stiffness: 150,
    damping: 12,
  });
  const height = useSpring(heightTransform, {
    mass: 0.1,
    stiffness: 150,
    damping: 12,
  });

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <motion.div
            ref={ref}
            style={{ width, height }}
            onClick={isClickable ? onClick : undefined}
            className={cn(
              "relative flex aspect-square items-center justify-center rounded-xl border border-white/20",
              isActive ? "bg-white/90 shadow-lg" : "bg-white/70",
              isClickable ? "cursor-pointer" : "cursor-default"
            )}
          >
            <div className="flex h-full w-full items-center justify-center overflow-hidden">
              {icon}
            </div>
            {isActive && (
              <div
                className={`absolute -bottom-1.5 left-1/2 -translate-x-1/2 h-1 w-1 rounded-full shadow-sm ${
                  isMinimized ? "bg-red/70 ring-1 ring-white" : "bg-white"
                }`}
              />
            )}
          </motion.div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{label}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
