"use client";

import Image from "next/image";
import { ReactNode } from "react";

interface WindowIconProps {
  src: string;
  alt: string;
  className?: string;
  children?: ReactNode;
}

export function WindowIcon({
  src,
  alt,
  className = "",
  children,
}: WindowIconProps) {
  return (
    <div className={`relative w-full h-full ${className}`}>
      <Image
        src={src}
        alt={alt}
        fill
        sizes="(max-width: 80px) 100vw"
        quality={100}
        className="object-cover"
        style={{ imageRendering: "auto", borderRadius: "12px" }}
      />
      {children}
    </div>
  );
}
