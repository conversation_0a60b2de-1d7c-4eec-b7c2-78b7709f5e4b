"use client";

import { ReactNode } from "react";
import { WindowIcon } from "./window-icon";

export interface DockItem {
  id: string;
  icon: ReactNode;
  label: string;
  isActive?: boolean;
  isMinimized?: boolean;
  isClickable?: boolean;
  onClick?: () => void;
}

interface CreateDockItemOptions {
  id: string;
  label: string;
  iconSrc?: string;
  icon?: ReactNode;
  isActive?: boolean;
  isMinimized?: boolean;
  isClickable?: boolean;
  onClick?: () => void;
}

export function createDockItem({
  id,
  label,
  iconSrc,
  icon,
  isActive = false,
  isMinimized = false,
  isClickable = false,
  onClick,
}: CreateDockItemOptions): DockItem {
  const dockIcon = iconSrc ? <WindowIcon src={iconSrc} alt={label} /> : icon;

  if (!dockIcon) {
    throw new Error(
      "Either iconSrc or icon must be provided to createDockItem"
    );
  }

  return {
    id,
    label,
    icon: dockIcon,
    isActive,
    isMinimized,
    isClickable,
    onClick,
  };
}
