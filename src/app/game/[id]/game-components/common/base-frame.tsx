"use client";

import { ReactNode } from "react";
import { cn } from "@/lib/utils";

interface BaseFrameProps {
  children: ReactNode;
  className?: string;
  headerContent?: ReactNode;
  footerContent?: ReactNode;
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
  fullHeight?: boolean;
}

export function BaseFrame({
  children,
  className = "",
  headerContent,
  footerContent,
  backgroundColor = "bg-white",
  textColor = "text-black",
  borderColor = "border-gray-200",
  fullHeight = true,
}: BaseFrameProps) {
  return (
    <div
      className={cn(
        "flex flex-col overflow-hidden border shadow-lg",
        backgroundColor,
        textColor,
        borderColor,
        fullHeight ? "h-full" : "",
        className
      )}
    >
      {headerContent && (
        <div className="flex-shrink-0 border-b border-inherit">
          {headerContent}
        </div>
      )}

      <div className="flex-1 overflow-auto">{children}</div>

      {footerContent && (
        <div className="flex-shrink-0 border-t border-inherit">
          {footerContent}
        </div>
      )}
    </div>
  );
}
