"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";

export type WindowType = "chrome" | "notes" | "ai" | "zoom";

export type WindowState = "open" | "minimized" | "closed";

export interface WindowConfig {
  id: string;
  type: WindowType;
  title: string;
  icon: ReactNode;
  content: ReactNode;
  position?: { x: number; y: number };
  size?: { width: number; height: number };
  isOpen?: boolean;
  isMinimized?: boolean;
  isFullScreen?: boolean;
  previousPosition?: { x: number; y: number };
  previousSize?: { width: number; height: number };
  zIndex?: number;
}

interface MacOSContextType {
  windows: WindowConfig[];
  activeWindowId: string | null;
  openWindow: (windowId: string) => void;
  closeWindow: (windowId: string) => void;
  minimizeWindow: (windowId: string) => void;
  toggleFullScreen: (windowId: string) => void;
  setActiveWindow: (windowId: string) => void;
  updateWindowPosition: (
    windowId: string,
    position: { x: number; y: number }
  ) => void;
  updateWindowSize: (
    windowId: string,
    size: { width: number; height: number }
  ) => void;
  getWindowById: (windowId: string) => WindowConfig | undefined;
  isWindowOpen: (windowId: string) => boolean;
  isWindowMinimized: (windowId: string) => boolean;
}

const MacOSContext = createContext<MacOSContextType>({
  windows: [],
  activeWindowId: null,
  openWindow: () => {},
  closeWindow: () => {},
  minimizeWindow: () => {},
  toggleFullScreen: () => {},
  setActiveWindow: () => {},
  updateWindowPosition: () => {},
  updateWindowSize: () => {},
  getWindowById: () => undefined,
  isWindowOpen: () => false,
  isWindowMinimized: () => false,
});

interface MacOSProviderProps {
  children: ReactNode;
  initialWindows: WindowConfig[];
}

export function MacOSProvider({
  children,
  initialWindows,
}: MacOSProviderProps) {
  const [windows, setWindows] = useState<WindowConfig[]>(
    initialWindows.map((window, index) => ({
      ...window,
      isOpen: window.isOpen || false,
      zIndex: window.zIndex || index,
      position: window.position || { x: 100 + index * 30, y: 100 + index * 30 },
      size: window.size || { width: 800, height: 600 },
    }))
  );
  const [activeWindowId, setActiveWindowId] = useState<string | null>(null);

  const getHighestZIndex = () => {
    return windows.reduce(
      (highest, window) => Math.max(highest, window.zIndex || 0),
      0
    );
  };

  const openWindow = (windowId: string) => {
    setWindows((prevWindows) =>
      prevWindows.map((window) => {
        if (window.id === windowId) {
          return {
            ...window,
            isOpen: true,
            isMinimized: false,
            zIndex: getHighestZIndex() + 1,
          };
        }
        return window;
      })
    );
    setActiveWindowId(windowId);
  };

  const closeWindow = (windowId: string) => {
    setWindows((prevWindows) =>
      prevWindows.map((window) => {
        if (window.id === windowId) {
          return { ...window, isOpen: false, isMinimized: false };
        }
        return window;
      })
    );

    if (activeWindowId === windowId) {
      const openWindows = windows.filter((w) => w.isOpen && w.id !== windowId);
      if (openWindows.length > 0) {
        const nextActiveWindow = openWindows.reduce(
          (highest, window) =>
            (window.zIndex || 0) > (highest.zIndex || 0) ? window : highest,
          openWindows[0]
        );
        setActiveWindowId(nextActiveWindow.id);
      } else {
        setActiveWindowId(null);
      }
    }
  };

  const minimizeWindow = (windowId: string) => {
    setWindows((prevWindows) =>
      prevWindows.map((window) => {
        if (window.id === windowId) {
          return { ...window, isOpen: false, isMinimized: true };
        }
        return window;
      })
    );

    if (activeWindowId === windowId) {
      const openWindows = windows.filter((w) => w.isOpen && w.id !== windowId);
      if (openWindows.length > 0) {
        const nextActiveWindow = openWindows.reduce(
          (highest, window) =>
            (window.zIndex || 0) > (highest.zIndex || 0) ? window : highest,
          openWindows[0]
        );
        setActiveWindowId(nextActiveWindow.id);
      } else {
        setActiveWindowId(null);
      }
    }
  };

  const setActiveWindow = (windowId: string) => {
    setWindows((prevWindows) =>
      prevWindows.map((window) => {
        if (window.id === windowId) {
          return {
            ...window,
            zIndex: getHighestZIndex() + 1,
          };
        }
        return window;
      })
    );
    setActiveWindowId(windowId);
  };

  const updateWindowPosition = (
    windowId: string,
    position: { x: number; y: number }
  ) => {
    setWindows((prevWindows) =>
      prevWindows.map((window) => {
        if (window.id === windowId) {
          return { ...window, position };
        }
        return window;
      })
    );
  };

  const updateWindowSize = (
    windowId: string,
    size: { width: number; height: number }
  ) => {
    setWindows((prevWindows) =>
      prevWindows.map((window) => {
        if (window.id === windowId) {
          return { ...window, size };
        }
        return window;
      })
    );
  };

  const getWindowById = (windowId: string) => {
    return windows.find((window) => window.id === windowId);
  };

  const isWindowOpen = (windowId: string) => {
    const window = getWindowById(windowId);
    return window ? !!window.isOpen : false;
  };

  const isWindowMinimized = (windowId: string) => {
    const window = getWindowById(windowId);
    return window ? !!window.isMinimized : false;
  };

  const toggleFullScreen = (windowId: string) => {
    const getWindowDimensions = () => {
      if (typeof document !== "undefined") {
        return {
          width: document.documentElement.clientWidth || 1200,
          height: (document.documentElement.clientHeight || 800) - 105,
        };
      }
      return { width: 1200, height: 800 };
    };

    setWindows((prevWindows) =>
      prevWindows.map((window) => {
        if (window.id === windowId) {
          if (window.isFullScreen) {
            return {
              ...window,
              isFullScreen: false,
              position: window.previousPosition || window.position,
              size: window.previousSize || window.size,
            };
          } else {
            const dimensions = getWindowDimensions();
            return {
              ...window,
              isFullScreen: true,
              previousPosition: window.position,
              previousSize: window.size,
              position: { x: 0, y: 24 },
              size: dimensions,
            };
          }
        }
        return window;
      })
    );
    setActiveWindow(windowId);
  };

  const contextValue = React.useMemo(
    () => ({
      windows,
      activeWindowId,
      openWindow,
      closeWindow,
      minimizeWindow,
      toggleFullScreen,
      setActiveWindow,
      updateWindowPosition,
      updateWindowSize,
      getWindowById,
      isWindowOpen,
      isWindowMinimized,
    }),
    [windows, activeWindowId]
  );

  return (
    <MacOSContext.Provider value={contextValue}>
      {children}
    </MacOSContext.Provider>
  );
}

export function useMacOS() {
  return useContext(MacOSContext);
}
