import { Battery, Wifi, Volume2 } from "lucide-react";

export function MacOSTopBar() {
  const currentDate = new Date();
  const formattedTime = currentDate.toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  });
  const formattedDate = currentDate.toLocaleDateString("en-US", {
    weekday: "short",
    month: "short",
    day: "numeric",
  });

  return (
    <div className="fixed top-0 left-0 right-0 z-50 flex h-6 w-full items-center justify-between bg-black/80 px-2 text-xs text-white backdrop-blur-md">
      <div className="flex items-center space-x-4">
        <div className="flex items-center font-semibold">
          <svg className="h-4 w-4 fill-current" viewBox="0 0 24 24">
            <path d="M18.71,19.5C17.88,20.74 17,21.95 15.66,21.97C14.32,22 13.89,21.18 12.37,21.18C10.84,21.18 10.37,21.95 9.1,22C7.79,22.05 6.8,20.68 5.96,19.47C4.25,17 2.94,12.45 4.7,9.39C5.57,7.87 7.13,6.91 8.82,6.88C10.1,6.86 11.32,7.75 12.11,7.75C12.89,7.75 14.37,6.68 15.92,6.84C16.57,6.87 18.39,7.1 19.56,8.82C19.47,8.88 17.39,10.1 17.41,12.63C17.44,15.65 20.06,16.66 20.09,16.67C20.06,16.74 19.67,18.11 18.71,19.5M13,3.5C13.73,2.67 14.94,2.04 15.94,2C16.07,3.17 15.6,4.35 14.9,5.19C14.21,6.04 13.07,6.7 11.95,6.61C11.8,5.46 12.36,4.26 13,3.5Z" />
          </svg>
          <span className="ml-1">Finder</span>
        </div>
        {["File", "Edit", "View", "Go", "Window", "Help"].map((menuItem) => (
          <div key={menuItem}>{menuItem}</div>
        ))}
      </div>
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-1">
          <Wifi className="h-3.5 w-3.5" />
          <Battery className="h-3.5 w-3.5" />
          <Volume2 className="h-3.5 w-3.5" />
        </div>
        <div>{formattedDate}</div>
        <div>{formattedTime}</div>
      </div>
    </div>
  );
}
