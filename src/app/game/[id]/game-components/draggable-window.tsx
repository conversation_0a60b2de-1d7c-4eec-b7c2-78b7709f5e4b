"use client";

import React, { useState, useRef, useEffect, ReactNode } from "react";
import { XIcon, MinusIcon, Maximize2 } from "lucide-react";
import { useMacOS } from "./macos-context";
import { cn } from "@/lib/utils";

interface DraggableWindowProps {
  id: string;
  title: string;
  children: ReactNode;
  className?: string;
  initialPosition?: { x: number; y: number };
  initialSize?: { width: number; height: number };
  minWidth?: number;
  minHeight?: number;
  resizable?: boolean;
}

export function DraggableWindow({
  id,
  title,
  children,
  className,
  initialPosition = { x: 100, y: 100 },
  initialSize = { width: 800, height: 600 },
  minWidth = 400,
  minHeight = 300,
  resizable = true,
}: DraggableWindowProps) {
  const {
    activeWindowId,
    setActiveWindow,
    closeWindow,
    minimizeWindow,
    toggleFullScreen,
    updateWindowPosition,
    updateWindowSize,
    getWindowById,
  } = useMacOS();

  const windowRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const resizeHandleRef = useRef<HTMLDivElement>(null);

  const window = getWindowById(id);
  const position = window?.position || initialPosition;
  const size = window?.size || initialSize;
  const zIndex = window?.zIndex || 1;
  const isActive = activeWindowId === id;
  const isFullScreen = window?.isFullScreen || false;

  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  const [isResizing, setIsResizing] = useState(false);
  const [resizeStart, setResizeStart] = useState({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
    corner: "bottom-right" as
      | "top-left"
      | "top-right"
      | "bottom-left"
      | "bottom-right",
  });

  const handleWindowClick = () => {
    if (!isActive) {
      setActiveWindow(id);
    }
  };

  const handleDragStart = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!headerRef.current?.contains(e.target as Node)) return;

    setIsDragging(true);
    setDragOffset({
      x: e.clientX - position.x,
      y: e.clientY - position.y,
    });

    if (!isActive) {
      setActiveWindow(id);
    }
  };

  const handleResizeStart = (
    e: React.MouseEvent<HTMLDivElement>,
    corner: "top-left" | "top-right" | "bottom-left" | "bottom-right"
  ) => {
    e.stopPropagation();
    setIsResizing(true);
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: size.width,
      height: size.height,
      corner: corner,
    });

    if (!isActive) {
      setActiveWindow(id);
    }
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        const newX = e.clientX - dragOffset.x;

        const newY = Math.max(24, e.clientY - dragOffset.y);
        updateWindowPosition(id, { x: newX, y: newY });
      } else if (isResizing) {
        let newWidth = size.width;
        let newHeight = size.height;
        let newX = position.x;
        let newY = position.y;

        switch (resizeStart.corner) {
          case "bottom-right":
            newWidth = Math.max(
              minWidth,
              resizeStart.width + (e.clientX - resizeStart.x)
            );
            newHeight = Math.max(
              minHeight,
              resizeStart.height + (e.clientY - resizeStart.y)
            );
            break;

          case "bottom-left":
            newWidth = Math.max(
              minWidth,
              resizeStart.width - (e.clientX - resizeStart.x)
            );
            newHeight = Math.max(
              minHeight,
              resizeStart.height + (e.clientY - resizeStart.y)
            );
            newX = position.x + (size.width - newWidth);
            break;

          case "top-right":
            newWidth = Math.max(
              minWidth,
              resizeStart.width + (e.clientX - resizeStart.x)
            );
            newHeight = Math.max(
              minHeight,
              resizeStart.height - (e.clientY - resizeStart.y)
            );
            newY = position.y + (size.height - newHeight);
            break;

          case "top-left":
            newWidth = Math.max(
              minWidth,
              resizeStart.width - (e.clientX - resizeStart.x)
            );
            newHeight = Math.max(
              minHeight,
              resizeStart.height - (e.clientY - resizeStart.y)
            );
            newX = position.x + (size.width - newWidth);
            newY = position.y + (size.height - newHeight);
            break;
        }

        if (newX !== position.x || newY !== position.y) {
          updateWindowPosition(id, { x: newX, y: Math.max(12, newY) });
        }

        updateWindowSize(id, { width: newWidth, height: newHeight });
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      setIsResizing(false);
    };

    if (isDragging || isResizing) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    }

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [
    isDragging,
    isResizing,
    dragOffset,
    resizeStart,
    id,
    minWidth,
    minHeight,
    updateWindowPosition,
    updateWindowSize,
  ]);

  return (
    <div
      ref={windowRef}
      className={cn(
        "absolute rounded-lg overflow-hidden border border-gray-200 bg-white flex flex-col",
        isActive ? "shadow-[0_0_15px_rgba(0,0,0,0.2)]" : "shadow-lg",
        isFullScreen ? "rounded-none" : "",
        className
      )}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        width: `${size.width}px`,
        height: `${size.height}px`,
        zIndex,
      }}
      onClick={handleWindowClick}
    >
      <div
        ref={headerRef}
        className={cn(
          "h-8 flex items-center justify-between px-3",
          !isFullScreen ? "cursor-move" : "",
          isActive ? "bg-gray-200" : "bg-gray-100"
        )}
        onMouseDown={!isFullScreen ? handleDragStart : undefined}
      >
        <div className="group flex space-x-2">
          <button
            className="w-3 h-3 rounded-full bg-red-500 flex items-center justify-center cursor-pointer hover:bg-red-400 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              closeWindow(id);
            }}
          >
            <XIcon className="w-2 h-2 text-red-800 opacity-0 group-hover:opacity-100 hover:opacity-100" />
          </button>
          <button
            className="w-3 h-3 rounded-full bg-yellow-500 flex items-center justify-center cursor-pointer hover:bg-yellow-400 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              minimizeWindow(id);
            }}
          >
            <MinusIcon className="w-2 h-2 text-yellow-800 opacity-0 group-hover:opacity-100 hover:opacity-100" />
          </button>
          <button
            className="w-3 h-3 rounded-full bg-green-500 flex items-center justify-center cursor-pointer hover:bg-green-400 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              toggleFullScreen(id);
            }}
          >
            <Maximize2 className="w-2 h-2 text-green-800 opacity-0 group-hover:opacity-100 hover:opacity-100" />
          </button>
        </div>

        <div className="absolute left-0 right-0 flex justify-center pointer-events-none">
          <span className="text-xs font-medium text-gray-700">{title}</span>
        </div>
      </div>

      <div className="flex-1 overflow-auto">{children}</div>

      {resizable && !isFullScreen && (
        <>
          <div
            ref={resizeHandleRef}
            className="absolute bottom-0 right-0 w-4 h-4 cursor-nwse-resize"
            onMouseDown={(e) => handleResizeStart(e, "bottom-right")}
          />

          <div
            className="absolute bottom-0 left-0 w-4 h-4 cursor-nesw-resize"
            onMouseDown={(e) => handleResizeStart(e, "bottom-left")}
          />

          <div
            className="absolute top-0 right-0 w-4 h-4 cursor-nesw-resize"
            onMouseDown={(e) => handleResizeStart(e, "top-right")}
          />

          <div
            className="absolute top-0 left-0 w-4 h-4 cursor-nwse-resize"
            onMouseDown={(e) => handleResizeStart(e, "top-left")}
          />
        </>
      )}
    </div>
  );
}
