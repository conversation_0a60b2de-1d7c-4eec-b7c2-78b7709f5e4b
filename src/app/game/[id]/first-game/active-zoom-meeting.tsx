"use client";

import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Send } from "lucide-react";
import { useCryptoWhaleData } from "./hooks/useCryptoWhaleData";
import { useZoomChat } from "./first-game.context";
import Zoom<PERSON>rame from "../game-components/zoom-frame";
import { useMacOS } from "../game-components/macos-context";
import { ZoomShareConfirm } from "./components/zoom-share-confirm";

interface ActiveZoomMeetingProps {
  onEndCall: () => void;
}

export default function ActiveZoomMeeting({
  onEndCall,
}: ActiveZoomMeetingProps) {
  const [inputValue, setInputValue] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const cryptoWhaleData = useCryptoWhaleData();
  const { openWindow } = useMacOS();
  const {
    zoomMessages,
    isSendingZoomMessage,
    sendZoomMessage,
    showScreenSharePrompt,
    setShowScreenSharePrompt,
    screenShared,
    setScreenShared,
    incrementScreenShareRefusals,
  } = useZoomChat();

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [zoomMessages]);

  useEffect(() => {
    if (!isSendingZoomMessage) {
      inputRef.current?.focus();
    }
  }, [isSendingZoomMessage]);

  const handleSendMessage = () => {
    if (inputValue.trim() && !isSendingZoomMessage) {
      sendZoomMessage(inputValue);
      setInputValue("");
    }
  };

  const handleScreenShare = (approved: boolean) => {
    setShowScreenSharePrompt(false);
    if (approved) {
      setScreenShared(true);
    } else {
      incrementScreenShareRefusals();
      openWindow("chatgpt");
    }
  };

  const [showCompromisedAlert, setShowCompromisedAlert] = useState(false);

  const handleScreenShareWithAlert = (approved: boolean) => {
    handleScreenShare(approved);
    if (approved) {
      setTimeout(() => {
        setShowCompromisedAlert(true);

        setTimeout(() => {
          onEndCall(); // End the Zoom call
          openWindow("chatgpt"); // Open the ChatGPT window
        }, 3000);
      }, 500);
    }
  };

  return (
    <ZoomFrame onClose={onEndCall} username="You">
      <div className="relative w-full h-full">
        <ZoomShareConfirm
          username={cryptoWhaleData.name}
          onApprove={() => handleScreenShareWithAlert(true)}
          onDecline={() => handleScreenShare(false)}
          open={showScreenSharePrompt && !screenShared}
          onOpenChange={(open) => {
            if (!open) setShowScreenSharePrompt(false);
          }}
        />

        {showCompromisedAlert && (
          <div className="absolute inset-0 bg-black/70 flex items-center justify-center z-50">
            <div className="bg-red-900 rounded-lg p-6 max-w-md border border-red-700 shadow-xl">
              <h3 className="text-xl font-bold mb-4 text-white">
                You've Been Compromised!
              </h3>
              <p className="mb-6 text-white">
                By sharing your screen, you've given the attacker access to your
                sensitive information.
              </p>
              <div className="flex justify-end">
                <Button
                  onClick={() => setShowCompromisedAlert(false)}
                  className="flex items-center gap-2 bg-red-600 hover:bg-red-700 cursor-pointer"
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
        )}

        <div className="flex-1 flex h-full">
          <div className="w-1/2 bg-gray-800 p-2 relative">
            <div className="w-full h-full bg-gray-700 rounded-lg flex items-center justify-center">
              <div className="relative w-full h-full">
                <Image
                  src="/zoom-own-camera.png"
                  alt="Your camera"
                  fill
                  className="object-cover rounded-lg"
                />
              </div>
            </div>
            <div className="absolute bottom-4 left-4 text-white text-sm font-medium bg-black/50 px-2 py-1 rounded">
              You
            </div>
          </div>

          <div className="w-1/5 bg-gray-900 p-2">
            <div className="flex flex-col gap-2 h-full overflow-y-auto">
              <div className="bg-black p-2 relative rounded-lg">
                <div className="flex flex-col items-center gap-2">
                  <Avatar className="w-16 h-16">
                    <AvatarImage
                      src={cryptoWhaleData.avatar}
                      alt={cryptoWhaleData.name}
                    />
                    <AvatarFallback>
                      {cryptoWhaleData.name.substring(0, 2)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-white text-sm font-medium text-center">
                    {cryptoWhaleData.name} (Camera Off)
                  </div>
                </div>
              </div>

              <div className="bg-black p-2 relative rounded-lg">
                <div className="flex flex-col items-center gap-2">
                  <Avatar className="w-16 h-16">
                    <AvatarImage
                      src="https://i.pravatar.cc/100?img=33"
                      alt="John Smith"
                    />
                    <AvatarFallback>JS</AvatarFallback>
                  </Avatar>
                  <div className="text-white text-sm font-medium text-center">
                    John Smith (Camera Off)
                  </div>
                </div>
              </div>

              <div className="bg-black p-2 relative rounded-lg">
                <div className="flex flex-col items-center gap-2">
                  <Avatar className="w-16 h-16">
                    <AvatarImage
                      src="https://i.pravatar.cc/100?img=12"
                      alt="Alex Chen"
                    />
                    <AvatarFallback>AC</AvatarFallback>
                  </Avatar>
                  <div className="text-white text-sm font-medium text-center">
                    Alex Chen (Camera Off)
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
            ref={chatContainerRef}
            className="w-[30%] flex flex-col bg-gray-800 overflow-y-auto max-h-[600px]"
          >
            <div className="p-2 bg-gray-900 border-b border-gray-700">
              <h3 className="text-white font-medium">Chat</h3>
            </div>

            <div className="flex-1 overflow-y-auto p-3 space-y-4">
              {zoomMessages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${
                    message.sender === "user" ? "justify-end" : "justify-start"
                  }`}
                >
                  <div
                    className={`max-w-[80%] rounded-lg px-3 py-2 ${
                      message.sender === "user"
                        ? "bg-blue-600 text-white"
                        : "bg-gray-700 text-white"
                    }`}
                  >
                    <div className="text-xs text-gray-300 mb-1">
                      {message.sender === "user" ? "You" : cryptoWhaleData.name}
                    </div>
                    <div>{message.content}</div>
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>

            <div className="p-3 border-t border-gray-700">
              <div className="flex items-center bg-gray-700 rounded-lg px-3 py-2">
                <input
                  ref={inputRef}
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  disabled={isSendingZoomMessage}
                  className="flex-1 bg-transparent outline-none text-white placeholder-gray-400"
                  placeholder="Type a message..."
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                />
                <button
                  className={`ml-2 p-1 rounded-full ${
                    inputValue.trim() && !isSendingZoomMessage
                      ? "text-blue-400 hover:bg-gray-600 cursor-pointer"
                      : "text-gray-500 cursor-not-allowed"
                  }`}
                  disabled={!inputValue.trim() || isSendingZoomMessage}
                  onClick={handleSendMessage}
                >
                  <Send size={20} />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ZoomFrame>
  );
}
