"use client";

import Link from "next/link";
import { useCryptoWhaleData } from "./hooks/useCryptoWhaleData";
import { useScreenSharing } from "./first-game.context";
import TwitterSignInScreen from "./twitter-sign-in-screen";
import { TwitterLayout } from "./components/twitter-layout";
import { TwitterFollowersTabs } from "./components/twitter-followers-tabs";
import { generateRandomFollowers } from "./utils/twitter-utils";
import type { FollowersTab } from "./components/twitter-followers-tabs";

interface CryptoWhaleFollowersPageProps {
  initialTab?: FollowersTab;
}

export default function CryptoWhaleFollowersPage({
  initialTab = "verified",
}: CryptoWhaleFollowersPageProps = {}) {
  const cryptoWhaleData = useCryptoWhaleData();
  const { screenShared } = useScreenSharing();

  const verifiedFollowers = generateRandomFollowers(34, 0, 0.5, "web3");
  const followers = generateRandomFollowers(120, 100, 0.2, "crypto");
  const following = generateRandomFollowers(110, 300, 0.3, "digital");

  if (screenShared) {
    return <TwitterSignInScreen />;
  }

  return (
    <TwitterLayout>
      <div className="sticky top-0 z-10 backdrop-blur-md bg-black/70 p-4 border-b border-gray-800">
        <div className="flex items-center gap-6">
          <Link href="#" className="rounded-full hover:bg-gray-800 p-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M19 12H5M12 19l-7-7 7-7" />
            </svg>
          </Link>
          <div>
            <h1 className="font-bold text-xl">{cryptoWhaleData.name}</h1>
            <p className="text-gray-500 text-sm">{cryptoWhaleData.username}</p>
          </div>
        </div>
      </div>

      <TwitterFollowersTabs
        initialTab={initialTab}
        verifiedFollowers={verifiedFollowers}
        followers={followers}
        following={following}
      />
    </TwitterLayout>
  );
}
