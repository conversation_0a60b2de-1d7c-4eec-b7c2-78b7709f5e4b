"use client";

export const getRandomName = (index: number) => {
  const firstNames = [
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
  ];
  const lastNames = [
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
  ];

  const firstName = firstNames[index % firstNames.length];
  const lastName = lastNames[(index + 3) % lastNames.length];

  return `${firstName} ${lastName}`;
};

export const getRandomAvatar = (index: number) => {
  const name = getRandomName(index);
  const formattedName = name.replace(" ", "+");

  const services = [
    `https://i.pravatar.cc/150?img=${index % 70}`,
    `https://randomuser.me/api/portraits/${index % 2 === 0 ? "men" : "women"}/${
      index % 99
    }.jpg`,
    `https://avatars.dicebear.com/api/human/${index}.svg`,
    `https://ui-avatars.com/api/?name=${formattedName}&background=random`,
  ];
  return services[index % services.length];
};

export const getRandomUsername = (name: string) => {
  const randomNum = Math.floor(Math.random() * 1000);
  return `@${name.split(" ")[0].toLowerCase()}${randomNum}`;
};

export const generateRandomFollowers = (
  length: number,
  startIndex: number = 0,
  verifiedProbability: number = 0.5,
  bioType: "web3" | "crypto" | "digital" = "web3"
) => {
  return Array.from({ length }, (_, i) => {
    const index = i + startIndex;
    const name = getRandomName(index);

    let bio = "";
    switch (bioType) {
      case "web3":
        bio = `Web3 enthusiast | ${
          Math.random() > 0.5 ? "Developer" : "Investor"
        } | ${Math.random() > 0.5 ? "NFT collector" : "DeFi explorer"}`;
        break;
      case "crypto":
        bio = `${Math.random() > 0.5 ? "Crypto" : "Blockchain"} ${
          Math.random() > 0.5 ? "enthusiast" : "believer"
        } | ${Math.random() > 0.5 ? "Web3" : "Metaverse"} ${
          Math.random() > 0.5 ? "explorer" : "builder"
        }`;
        break;
      case "digital":
        bio = `${Math.random() > 0.5 ? "Digital" : "Virtual"} ${
          Math.random() > 0.5 ? "artist" : "creator"
        } | ${Math.random() > 0.5 ? "Podcast" : "Content"} ${
          Math.random() > 0.5 ? "host" : "producer"
        }`;
        break;
    }

    return {
      id: index,
      name,
      username: getRandomUsername(name),
      avatar: getRandomAvatar(index),
      isVerified: Math.random() > 1 - verifiedProbability,
      bio,
    };
  });
};

export interface TwitterFollower {
  id: number;
  name: string;
  username: string;
  avatar: string;
  isVerified: boolean;
  bio: string;
}
