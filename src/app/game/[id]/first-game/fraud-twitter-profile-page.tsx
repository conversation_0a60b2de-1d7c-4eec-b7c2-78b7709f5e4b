"use client";

import { useCryptoWhaleData } from "./hooks/useCryptoWhaleData";
import { useChrome } from "../game-components/chrome-context";
import { useScreenSharing } from "./first-game.context";
import CryptoWhaleFollowersPage from "./crypto-whale-followers-page";
import TwitterSignInScreen from "./twitter-sign-in-screen";
import { TwitterLayout } from "./components/twitter-layout";
import { TwitterProfileContent } from "./components/twitter-profile-content";

export default function TwitterProfile() {
  const cryptoWhaleData = useCryptoWhaleData();
  const { addTab, isTabOpen, openTabByName } = useChrome();
  const { screenShared } = useScreenSharing();

  const handleFollowersClick = () => {
    if (isTabOpen("CryptoWhale Followers")) {
      openTabByName("CryptoWhale Followers");
    } else {
      addTab({
        name: "CryptoWhale Followers",
        url: "https://twitter.com/crypto_whale/followers",
        closable: true,
        content: <CryptoWhaleFollowersPage initialTab="followers" />,
      });
    }
  };

  const handleFollowingClick = () => {
    if (isTabOpen("CryptoWhale Following")) {
      openTabByName("CryptoWhale Following");
    } else {
      addTab({
        name: "CryptoWhale Following",
        url: "https://twitter.com/crypto_whale/following",
        closable: true,
        content: <CryptoWhaleFollowersPage initialTab="following" />,
      });
    }
  };

  if (screenShared) {
    return <TwitterSignInScreen />;
  }

  return (
    <TwitterLayout>
      <TwitterProfileContent
        profileData={cryptoWhaleData}
        onFollowersClick={handleFollowersClick}
        onFollowingClick={handleFollowingClick}
        bannerImage="https://images.unsplash.com/photo-1639762681057-408e52192e55?q=80&w=2832&auto=format&fit=crop"
      />
    </TwitterLayout>
  );
}
