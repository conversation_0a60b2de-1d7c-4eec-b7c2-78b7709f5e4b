"use client";

import React from "react";
import MetamaskFrame from "../game-components/metamask-frame";
import { useMetamask, useScreenSharing } from "./first-game.context";

function getRandomPercentChange(symbol: string): number {
  const hash = symbol
    .split("")
    .reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const baseValue = (hash % 20) - 5;

  if (symbol === "TOPIA") return 8.46;
  if (symbol === "USDC") return -0.01;
  if (symbol === "ETH") return 7.56;
  if (symbol === "WETH") return 7.72;

  return parseFloat(baseValue.toFixed(2));
}

export default function MetamaskContent() {
  const { metamaskBalances } = useMetamask();
  const { screenShared } = useScreenSharing();

  const adjustedBalances = screenShared
    ? metamaskBalances.map((token) => ({
        ...token,
        balance: 0,
        usdValue: 0,
      }))
    : metamaskBalances;

  const transformedBalances = adjustedBalances.map((token) => ({
    ...token,
    percentChange: getRandomPercentChange(token.symbol),
  }));

  const totalBalance = adjustedBalances.reduce(
    (sum, token) => sum + token.usdValue,
    0
  );

  return (
    <MetamaskFrame
      balances={transformedBalances}
      totalBalance={totalBalance}
      percentChange={7.95}
      accountName="Account 1"
      accountAddress="0xAfcC3...6901b"
      networkName="Ethereum Mainnet"
    />
  );
}
