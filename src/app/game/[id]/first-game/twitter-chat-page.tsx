"use client";

import { Bell, Home, Mail, Search, Settings } from "lucide-react";
import { FaXTwitter } from "react-icons/fa6";
import { useChrome } from "../game-components/chrome-context";
import JobDescriptionPage from "./job-description-page";
import { useCryptoWhaleData } from "./hooks/useCryptoWhaleData";
import TwitterProfile from "./fraud-twitter-profile-page";
import CryptoWhaleFollowersPage from "./crypto-whale-followers-page";
import { useTwitterChat, useScreenSharing } from "./first-game.context";
import TwitterSignInScreen from "./twitter-sign-in-screen";
import { TwitterChatSidebar } from "./components/twitter-chat-sidebar";
import { TwitterChatContent } from "./components/twitter-chat-content";

interface TwitterChatPageProps {
  onJobDescriptionClick?: () => void;
  onProfileClick?: () => void;
  onFollowersClick?: () => void;
}

export function TwitterChatPage({
  onJobDescriptionClick,
  onProfileClick,
  onFollowersClick,
}: TwitterChatPageProps) {
  const { addTab, isTabOpen, openTabByName } = useChrome();
  const cryptoWhaleData = useCryptoWhaleData();
  const {
    twitterMessages,
    isSendingTwitterMessage,
    sendTwitterMessage,
    zoomLinkReceived,
  } = useTwitterChat();
  const { screenShared } = useScreenSharing();

  const handleJobDescriptionClick = () => {
    if (isTabOpen("Job Description")) {
      openTabByName("Job Description");
    } else {
      addTab({
        name: "Job Description",
        url: "https://nexusfi.com/careers/senior-defi-protocol-engineer",
        closable: true,
        content: <JobDescriptionPage />,
      });
    }

    if (onJobDescriptionClick) {
      onJobDescriptionClick();
    }
  };

  const handleProfileClick = () => {
    if (isTabOpen("CryptoWhale Profile")) {
      openTabByName("CryptoWhale Profile");
    } else {
      addTab({
        name: "CryptoWhale Profile",
        url: "https://twitter.com/crypto_whale",
        closable: true,
        content: <TwitterProfile />,
      });
    }

    if (onProfileClick) {
      onProfileClick();
    }
  };

  const handleFollowersClick = () => {
    if (isTabOpen("CryptoWhale Followers")) {
      openTabByName("CryptoWhale Followers");
    } else {
      addTab({
        name: "CryptoWhale Followers",
        url: "https://twitter.com/crypto_whale/followers",
        closable: true,
        content: <CryptoWhaleFollowersPage />,
      });
    }

    if (onFollowersClick) {
      onFollowersClick();
    }
  };

  if (screenShared) {
    return <TwitterSignInScreen />;
  }

  return (
    <div className="flex h-full flex-col bg-black text-white overflow-hidden">
      <header className="border-b border-gray-800 px-4 py-3 bg-black bg-opacity-90 backdrop-blur z-10 flex-shrink-0">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <div className="h-8 w-8 rounded-full bg-black flex items-center justify-center border border-gray-700">
              <FaXTwitter className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-xl font-bold ml-4">Messages</h1>
          </div>
          <div className="flex space-x-2">
            <Settings className="w-5 h-5 text-gray-400" />
            <Mail className="w-5 h-5 text-gray-400" />
          </div>
        </div>
      </header>

      <div className="flex flex-1 overflow-hidden">
        <TwitterChatSidebar activeUser={cryptoWhaleData} />

        <TwitterChatContent
          profileData={cryptoWhaleData}
          messages={twitterMessages}
          isSendingMessage={isSendingTwitterMessage}
          conversationEnded={zoomLinkReceived}
          onSendMessage={sendTwitterMessage}
          onProfileClick={handleProfileClick}
          onFollowersClick={handleFollowersClick}
          onJobDescriptionClick={handleJobDescriptionClick}
        />
      </div>

      <nav className="border-t border-gray-800 py-3 px-6 flex justify-around bg-black bg-opacity-90 backdrop-blur z-10 flex-shrink-0">
        <button className="text-white cursor-pointer">
          <Home className="w-6 h-6" />
        </button>
        <button className="text-gray-500 cursor-pointer">
          <Search className="w-6 h-6" />
        </button>
        <button className="text-gray-500 cursor-pointer">
          <Bell className="w-6 h-6" />
        </button>
        <button className="text-gray-500 cursor-pointer">
          <Mail className="w-6 h-6" />
        </button>
      </nav>
    </div>
  );
}
