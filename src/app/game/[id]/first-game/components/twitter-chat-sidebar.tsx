"use client";

import { CryptoWhaleData } from "../hooks/useCryptoWhaleData";
import { Verified } from "lucide-react";

interface TwitterChatSidebarProps {
  activeUser: CryptoWhaleData;
}

export function TwitterChatSidebar({ activeUser }: TwitterChatSidebarProps) {
  return (
    <div className="w-1/3 border-r border-gray-800 flex-shrink-0 overflow-y-auto">
      <div className="py-2 px-4 border-b border-gray-800 sticky top-0 bg-black z-10">
        <div className="flex items-center">
          <div className="font-bold">Message requests</div>
          <div className="ml-2 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center text-xs text-white font-bold">
            1
          </div>
        </div>
        <div className="text-xs text-gray-500">1 new message</div>
      </div>

      <div className="divide-y divide-gray-800 overflow-y-auto">
        <div className="p-3 flex items-center space-x-3 bg-gray-900 cursor-pointer relative">
          <div className="absolute right-2 top-2 w-3 h-3 bg-blue-500 rounded-full"></div>
          <div className="relative flex-shrink-0">
            <img
              src={activeUser.avatar}
              alt={activeUser.name}
              className="w-12 h-12 rounded-full object-cover border-2 border-blue-500"
            />
            <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-900"></span>
          </div>
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <span className="font-semibold text-white">
                  {activeUser.name}
                </span>
                {activeUser.isVerified && (
                  <Verified className="w-4 h-4 text-blue-500 ml-1" />
                )}
              </div>
              <span className="text-xs text-gray-400">2:19 PM</span>
            </div>
            <div className="text-sm font-medium text-white line-clamp-1">
              Hey! Been following your work on Web3...
            </div>
          </div>
        </div>

        <ChatSidebarItem
          avatar="https://i.pravatar.cc/100?img=33"
          name="ETH_Builder"
          message="Did you see the latest Arbitrum update?"
          timestamp="Yesterday"
        />

        <ChatSidebarItem
          avatar="https://i.pravatar.cc/100?img=12"
          name="DeFi_Maven"
          message="Check this MEV protection strategy 👀"
          timestamp="Apr 22"
        />

        <ChatSidebarItem
          avatar="https://i.pravatar.cc/100?img=47"
          name="NFT_Collector"
          message="You should mint this collection 🔥"
          timestamp="Apr 18"
        />

        <ChatSidebarItem
          avatar="https://i.pravatar.cc/100?img=22"
          name="Solana_Maxi"
          message="TPS still better than ETH lol"
          timestamp="Apr 12"
        />

        <ChatSidebarItem
          avatar="https://i.pravatar.cc/100?img=56"
          name="ZK_Researcher"
          message="New ZK-rollup paper - thoughts?"
          timestamp="Mar 29"
        />
      </div>
    </div>
  );
}

interface ChatSidebarItemProps {
  avatar: string;
  name: string;
  message: string;
  timestamp: string;
}

function ChatSidebarItem({
  avatar,
  name,
  message,
  timestamp,
}: ChatSidebarItemProps) {
  return (
    <div className="p-3 flex items-center space-x-3 hover:bg-gray-900 cursor-pointer">
      <div className="relative flex-shrink-0">
        <img
          src={avatar}
          alt={name}
          className="w-12 h-12 rounded-full object-cover"
        />
      </div>
      <div className="flex-1">
        <div className="flex items-center justify-between">
          <span className="font-semibold">{name}</span>
          <span className="text-xs text-gray-400">{timestamp}</span>
        </div>
        <div className="text-sm text-gray-400 truncate">{message}</div>
      </div>
    </div>
  );
}
