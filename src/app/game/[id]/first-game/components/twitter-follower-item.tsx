"use client";

import { useState } from "react";
import { Verified } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { TwitterFollower } from "../utils/twitter-utils";

interface FollowerItemProps {
  follower: TwitterFollower;
}

export function FollowerItem({ follower }: FollowerItemProps) {
  const [isFollowing, setIsFollowing] = useState(false);

  return (
    <div className="border-b border-gray-800 p-4 hover:bg-gray-900/50 transition">
      <div className="flex justify-between">
        <div className="flex gap-3">
          <Avatar className="w-12 h-12">
            <AvatarImage src={follower.avatar} alt={follower.name} />
            <AvatarFallback>{follower.name.substring(0, 2)}</AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center gap-1">
              <span className="font-bold">{follower.name}</span>
              {follower.isVerified && (
                <Verified className="w-4 h-4 text-[#1d9bf0]" />
              )}
            </div>
            <div className="text-gray-500">{follower.username}</div>
            <p className="mt-1 text-sm">{follower.bio}</p>
          </div>
        </div>
        <Button
          variant={isFollowing ? "outline" : "default"}
          className={`rounded-full font-bold h-9 cursor-pointer ${
            isFollowing
              ? "border-gray-600 text-white hover:border-red-500 hover:text-red-500 hover:bg-red-500/10"
              : "bg-white text-black hover:bg-gray-200"
          }`}
          onClick={() => setIsFollowing(!isFollowing)}
        >
          {isFollowing ? "Following" : "Follow"}
        </Button>
      </div>
    </div>
  );
}
