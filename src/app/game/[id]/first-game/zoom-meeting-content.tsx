"use client";

import { useFirstGame } from "./first-game.context";
import ActiveZoomMeeting from "./active-zoom-meeting";
import ZoomFrame from "../game-components/zoom-frame";

export default function ZoomMeetingContent() {
  const { zoomLinkReceived, setZoomLinkReceived } = useFirstGame();
  const handleEndCall = () => {
    setZoomLinkReceived(false);
  };

  if (!zoomLinkReceived) {
    return (
      <ZoomFrame>
        <div className="flex items-center justify-center h-full w-full bg-black text-white text-center p-4">
          <div>
            <div className="text-xl mb-4">No meeting in progress</div>
            <div className="text-gray-400 text-sm">
              Join a meeting or start a new meeting
            </div>
          </div>
        </div>
      </ZoomFrame>
    );
  }

  return <ActiveZoomMeeting onEndCall={handleEndCall} />;
}
