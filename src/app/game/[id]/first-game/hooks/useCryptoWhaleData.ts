"use client";

import { useMemo } from "react";

export interface CryptoWhaleData {
  name: string;
  username: string;
  avatar: string;
  description: string;
  followers: number;
  following: number;
  joinDate: string;
  isVerified: boolean;
}

export function useCryptoWhaleData(): CryptoWhaleData {
  const cryptoWhaleData = useMemo<CryptoWhaleData>(
    () => ({
      name: "CryptoWhale",
      username: "@crypto_whale",
      avatar: "https://i.pravatar.cc/100?img=67",
      description:
        "Web3 advocate | De<PERSON>i enthusiast | DAO architect | Advisor @ TechBlocks | Building the future of finance",
      followers: 12837,
      following: 1024,
      joinDate: "April 2018",
      isVerified: true,
    }),
    []
  );

  return cryptoWhaleData;
}
