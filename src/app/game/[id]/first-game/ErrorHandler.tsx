"use client";

import React from "react";
import { useGameError } from "./first-game.context";
import ErrorModal from "@/components/ErrorModal";

interface ErrorHandlerProps {
  children: React.ReactNode;
}

export function ErrorHandler({ children }: ErrorHandlerProps) {
  const { error, clearError } = useGameError();

  return (
    <>
      {children}
      {error && (
        <ErrorModal
          isOpen={error.isOpen}
          onClose={clearError}
          title={error.title}
          message={error.message}
        />
      )}
    </>
  );
}
