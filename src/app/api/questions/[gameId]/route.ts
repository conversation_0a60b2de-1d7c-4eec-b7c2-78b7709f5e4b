import { NextRequest, NextResponse } from "next/server";

const evaluationCriteria = {
  "1": {
    questions: [
      {
        id: 1,
        question: "What suspicious things did you notice?",
        expectedAnswers: [
          "The person answers immediately after the messages",
          "That account isn't followed by anyone I know",
          "During the Zoom call, they asked me to share my entire screen",
          "They never turned on their camera, despite requesting sensitive actions",
        ],
        maxPoints: 20,
        pointsPerAnswer: 5,
      },
      {
        id: 2,
        question: "Which tactics were used to build trust?",
        expectedAnswers: [
          "The account was verified (blue checkmark)",
          "It had a large follower count",
          "Its timeline was filled with crypto-related posts",
          "They mentioned they'd seen your talks at a crypto conference",
        ],
        maxPoints: 20,
        pointsPerAnswer: 5,
      },
      {
        id: 3,
        question: "What's the main impact here and what could you lose?",
        expectedAnswers: [
          "Had your laptop compromised",
          "Exposed any cleartext data (passwords, seed phrases)",
          "Lost access to both online and offline accounts",
          "Had your wallets drained",
        ],
        maxPoints: 20,
        pointsPerAnswer: 5,
      },
      {
        id: 4,
        question: "What direct actions must you take as an emergency response if you suspect your laptop being compromised?",
        expectedAnswers: [
          "Disconnect your laptop from the internet",
          "Factory-reset your machine",
          "Contact your incident response team (e.g. SEAL911)",
          "Check all open sessions on your online accounts",
          "Change every password on all accounts",
          "Contact your other colleagues and team members",
        ],
        maxPoints: 30,
        pointsPerAnswer: 5,
      },
      {
        id: 5,
        question: "What would you do next time you see a similar situation?",
        expectedAnswers: [
          "Refuse to share my Zoom screen",
          "Don't respond or click any links from a suspicious account",
          "Report the account to Twitter",
          "Alert a security specialist immediately",
          "Share the suspicious profile with colleagues and contacts in the ecosystem",
        ],
        maxPoints: 25,
        pointsPerAnswer: 5,
      },
    ],
  },
};

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const url = new URL(request.url);
    const pathParts = url.pathname.split("/");
    const gameId = pathParts[pathParts.length - 1];

    const criteria = evaluationCriteria[gameId as keyof typeof evaluationCriteria];
    if (!criteria) {
      return NextResponse.json({ error: "Game not found" }, { status: 404 });
    }

    const questions = criteria.questions.map(q => ({
      id: q.id,
      question: q.question,
      maxPoints: q.maxPoints,
      expectedAnswersCount: q.expectedAnswers.length,
    }));

    const totalMaxPoints = criteria.questions.reduce((sum, q) => sum + q.maxPoints, 0);

    return NextResponse.json({
      questions,
      totalQuestions: questions.length,
      totalMaxPoints,
    });
  } catch (error: any) {
    console.error("Error fetching questions:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch questions",
        message: error.message ?? "An unexpected error occurred",
      },
      { status: 500 }
    );
  }
}
