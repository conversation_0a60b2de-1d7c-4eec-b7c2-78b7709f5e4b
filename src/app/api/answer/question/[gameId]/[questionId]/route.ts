import { NextRequest, NextResponse } from "next/server";
import OpenAI from "openai";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const evaluationCriteria = {
  "1": {
    questions: [
      {
        id: 1,
        question: "What suspicious things did you notice?",
        expectedAnswers: [
          "The person answers immediately after the messages",
          "That account isn't followed by anyone I know",
          "During the Zoom call, they asked me to share my entire screen",
          "They never turned on their camera, despite requesting sensitive actions",
        ],
        maxPoints: 20,
        pointsPerAnswer: 5,
      },
      {
        id: 2,
        question: "Which tactics were used to build trust?",
        expectedAnswers: [
          "The account was verified (blue checkmark)",
          "It had a large follower count",
          "Its timeline was filled with crypto-related posts",
          "They mentioned they'd seen your talks at a crypto conference",
        ],
        maxPoints: 20,
        pointsPerAnswer: 5,
      },
      {
        id: 3,
        question: "What's the main impact here and what could you lose?",
        expectedAnswers: [
          "Had your laptop compromised",
          "Exposed any cleartext data (passwords, seed phrases)",
          "Lost access to both online and offline accounts",
          "Had your wallets drained",
        ],
        maxPoints: 20,
        pointsPerAnswer: 5,
      },
      {
        id: 4,
        question: "What direct actions must you take as an emergency response if you suspect your laptop being compromised?",
        expectedAnswers: [
          "Disconnect your laptop from the internet",
          "Factory-reset your machine",
          "Contact your incident response team (e.g. SEAL911)",
          "Check all open sessions on your online accounts",
          "Change every password on all accounts",
          "Contact your other colleagues and team members",
        ],
        maxPoints: 30,
        pointsPerAnswer: 5,
      },
      {
        id: 5,
        question: "What would you do next time you see a similar situation?",
        expectedAnswers: [
          "Refuse to share my Zoom screen",
          "Don't respond or click any links from a suspicious account",
          "Report the account to Twitter",
          "Alert a security specialist immediately",
          "Share the suspicious profile with colleagues and contacts in the ecosystem",
        ],
        maxPoints: 25,
        pointsPerAnswer: 5,
      },
    ],
  },
};

interface EvaluationResult {
  evaluation: CriterionMatch[];
}

interface CriterionMatch {
  criterion: string;
  p: number;
}

function getQuestion(gameId: string, questionId: number) {
  const criteria = evaluationCriteria[gameId as keyof typeof evaluationCriteria];
  if (!criteria) return null;
  
  return criteria.questions.find(q => q.id === questionId);
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const url = new URL(request.url);
    const pathParts = url.pathname.split("/");
    const gameId = pathParts[pathParts.length - 2];
    const questionId = parseInt(pathParts[pathParts.length - 1]);

    const { answer } = await request.json();

    const question = getQuestion(gameId, questionId);
    if (!question) {
      return NextResponse.json({ error: "Question not found" }, { status: 404 });
    }

    // Create evaluation request using o4-mini model
    const evaluationResponse = await openai.chat.completions.create({
      model: "o4-mini",
      messages: [
        {
          role: "system",
          content: `You are an expert evaluator for a cybersecurity awareness game. Your task is to analyze the player's answer and determine which expected answers they have correctly identified.

          Question: "${question.question}"
          
          Expected answers:
          ${question.expectedAnswers.map((a, i) => `${i + 1}. ${a}`).join('\n')}
          
          For each expected answer, assign a certainty score p between 0 and 1, where:
          - 1.0 means the expected answer is definitely present in the player's answer
          - 0.0 means the expected answer is definitely not present
          
          Only consider an answer matched if p > 0.8.
          
          Return your evaluation as a JSON array of objects with this format:

          evaluation: [
            { "criterion": "expected answer text", "p": 0.95 },
            { "criterion": "another expected answer", "p": 0.2 }
          ]`,
        },
        {
          role: "user",
          content: `Player's answer: "${answer}"\n\nExpected answers to evaluate:\n${question.expectedAnswers
            .map((a, i) => `${i + 1}. ${a}`)
            .join("\n")}`,
        },
      ],
      response_format: { type: "json_object" },
    });

    const evaluationText = evaluationResponse.choices[0].message.content;
    if (!evaluationText) {
      throw new Error("Failed to get evaluation from OpenAI");
    }

    const toolResults = JSON.parse(evaluationText) as EvaluationResult;
    const matchedCriteria = toolResults?.evaluation?.filter(
      (result: CriterionMatch) => result.p > 0.8
    );

    // Calculate points and format correct answers
    let totalPoints = 0;
    const correctAnswers: string[] = [];

    matchedCriteria.forEach((match: CriterionMatch) => {
      const isExpectedAnswer = question.expectedAnswers.includes(match.criterion);
      if (isExpectedAnswer) {
        totalPoints += question.pointsPerAnswer;
        correctAnswers.push(
          `${match.criterion} (${question.pointsPerAnswer} points)`
        );
      }
    });

    const result = {
      questionId: question.id,
      question: question.question,
      totalPoints: Math.min(totalPoints, question.maxPoints),
      maxPoints: question.maxPoints,
      correctAnswers,
      expectedAnswersCount: question.expectedAnswers.length,
    };

    return NextResponse.json(result);
  } catch (error: any) {
    console.error("Error evaluating question answer:", error);

    if (error.name === "OpenAIError" || error.message?.includes("OpenAI")) {
      return NextResponse.json(
        {
          error: "OpenAI API Error",
          message:
            error.message ??
            "There was an issue with the AI service. Please try again later.",
          code: error.code ?? "unknown_error",
        },
        { status: 502 }
      );
    }

    return NextResponse.json(
      {
        error: "Failed to evaluate answer",
        message: error.message ?? "An unexpected error occurred",
      },
      { status: 500 }
    );
  }
}
