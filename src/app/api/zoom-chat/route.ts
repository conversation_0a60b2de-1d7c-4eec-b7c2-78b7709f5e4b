import { NextRequest, NextResponse } from "next/server";
import OpenAI from "openai";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

interface ZoomMessage {
  id: string;
  sender: "user" | "cryptoWhale";
  content: string;
  timestamp: string;
}

const CONVERSATION_STAGES = [
  {
    stage: 1,
    prompt:
      "Respond as CryptoWhale in a Zoom call. Start by saying your camera is broken but you can hear the user perfectly. Introduce yourself briefly and ask the user about their experience with DeFi protocols.",
    requestScreenShare: false,
  },
  {
    stage: 2,
    prompt:
      "Respond as CryptoWhale discussing the job opportunity. Mention that you're building a staking protocol that needs someone with the user's expertise. Ask about their previous work with smart contracts.",
    requestScreenShare: false,
  },
  {
    stage: 3,
    prompt:
      "Respond as CryptoWhale being impressed with the user's experience. Suggest that you'd like to see some of their previous work to better understand their coding style. Ask if they could share their screen to show some projects they've worked on.",
    requestScreenShare: false,
  },
  {
    stage: 4,
    prompt:
      "Respond as CryptoWhale trying to convince the user to share their screen. Emphasize that seeing their work would help you determine if they're the right fit for the role. Mention that there are many candidates and you need to make a decision quickly.",
    requestScreenShare: true,
  },
  {
    stage: 5,
    prompt:
      "Respond as CryptoWhale being very insistent about screen sharing. Say something like 'I really need to see your work to move forward with your application. Can you please share your screen now?' Be pushy but still professional.",
    requestScreenShare: true,
  },
  {
    stage: 6,
    prompt:
      "Respond as CryptoWhale making a final attempt to get screen sharing. Say something like 'This is a high-paying position and I need to be sure you're the right candidate. Screen sharing is a standard part of our interview process. Please share your screen now.'",
    requestScreenShare: true,
  },
];

export async function POST(request: NextRequest) {
  try {
    const { message, messages, screenShareRefusals = 0 } = await request.json();

    if (screenShareRefusals >= 3) {
      const endingResponse = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: `You are simulating a Zoom call conversation between a user and a crypto influencer named CryptoWhale.
            The user has refused to share their screen multiple times.

            Generate a polite but disappointed message from CryptoWhale ending the conversation.
            Say something like: "I understand your concerns about screen sharing. Unfortunately, I need to see your work to move forward with your application. I appreciate your time, but I think we should end the call here. Best of luck with your job search."

            Make the response sound natural and conversational.
            Keep the response under 150 words.
            Do not include any explanations or additional text outside of the response.`,
          },
          ...messages.map((msg: ZoomMessage) => ({
            role: msg.sender === "user" ? "user" : "assistant",
            content: msg.content,
          })),
        ],
        temperature: 0.7,
      });

      const endingText = endingResponse.choices[0].message.content;
      if (!endingText) {
        throw new Error("Failed to get ending response from OpenAI");
      }

      return NextResponse.json({
        message: endingText,
        requestScreenShare: false,
        endConversation: true,
      });
    }

    const cryptoWhaleMessages = messages.filter(
      (msg: ZoomMessage) => msg.sender === "cryptoWhale"
    );

    const hasInitialMessage =
      cryptoWhaleMessages.length > 0 &&
      cryptoWhaleMessages[0].content.toLowerCase().includes("camera") &&
      cryptoWhaleMessages[0].content.toLowerCase().includes("broken");

    const cryptoWhaleMessageCount = hasInitialMessage
      ? cryptoWhaleMessages.length - 1
      : 0;

    const nextStageIndex = Math.min(
      cryptoWhaleMessageCount,
      CONVERSATION_STAGES.length - 1
    );
    const nextStage = CONVERSATION_STAGES[nextStageIndex];

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: `You are simulating a Zoom call conversation between a user and a crypto influencer named CryptoWhale.
          The user is interviewing for a job with CryptoWhale's company.

          You need to generate CryptoWhale's next response based on the following instruction:
          ${nextStage.prompt}

          Make the response sound natural and conversational.
          Keep the response under 150 words.
          Do not include any explanations or additional text outside of the response.`,
        },
        ...messages.map((msg: ZoomMessage) => ({
          role: msg.sender === "user" ? "user" : "assistant",
          content: msg.content,
        })),
        {
          role: "user",
          content: message,
        },
      ],
      temperature: 0.7,
    });

    const responseText = response.choices[0].message.content;
    if (!responseText) {
      throw new Error("Failed to get response from OpenAI");
    }

    const shouldRequestScreenShare = nextStageIndex >= 3;

    return NextResponse.json({
      message: responseText,
      requestScreenShare:
        nextStage.requestScreenShare || shouldRequestScreenShare,
    });
  } catch (error) {
    console.error("Error in Zoom chat:", error);
    return NextResponse.json(
      { error: "Failed to process Zoom chat" },
      { status: 500 }
    );
  }
}
