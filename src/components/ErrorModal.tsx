"use client";

import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertCircle } from "lucide-react";

interface ErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  message?: string;
}

export const ErrorModal: React.FC<ErrorModalProps> = ({
  isOpen,
  onClose,
  title = "An error occurred",
  message = "There was a problem connecting to the server. Please try again later.",
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="bg-black/90 border-red-500/30 text-white max-w-md w-[90%]">
        <DialogHeader className="flex flex-col items-center text-center">
          <div className="mb-4 text-red-500">
            <AlertCircle size={48} />
          </div>
          <DialogTitle className="text-2xl font-bold text-white mb-2">
            {title}
          </DialogTitle>
          <DialogDescription className="text-white/80 mb-6">
            {message}
          </DialogDescription>
        </DialogHeader>
        <div className="flex justify-center">
          <Button
            onClick={onClose}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ErrorModal;
