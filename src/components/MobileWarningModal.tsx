"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useIsMobile } from "@/hooks/useIsMobile";
import React, { useEffect, useState } from "react";

interface MobileWarningModalProps {
  isOpen?: boolean;
  onClose?: () => void;
  autoDetect?: boolean;
}

export const MobileWarningModal: React.FC<MobileWarningModalProps> = ({
  isOpen: externalIsOpen,
  onClose = () => {},
  autoDetect = false,
}) => {
  const isMobile = useIsMobile();
  const [internalIsOpen, setInternalIsOpen] = useState(false);

  const isOpen = autoDetect ? isMobile || internalIsOpen : externalIsOpen;

  useEffect(() => {
    if (autoDetect && isMobile) {
      setInternalIsOpen(true);
    }
  }, [autoDetect, isMobile]);

  const handleOpenChange = (open: boolean) => {
    if (autoDetect) {
      setInternalIsOpen(open);
    } else {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="bg-black/90 border-white/10 text-white max-w-md w-[90%]">
        <DialogHeader className="flex flex-col items-center text-center">
          <div className="mb-4">
            <svg
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              className="text-red-500"
            >
              <path
                d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                stroke="currentColor"
                strokeWidth="2"
              />
              <path
                d="M12 8V12"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <circle cx="12" cy="16" r="1" fill="currentColor" />
            </svg>
          </div>
          <DialogTitle className="text-2xl font-bold text-white mb-2">
            Desktop Only
          </DialogTitle>
          <DialogDescription className="text-white/80 mb-6">
            This game is only available on desktop devices with a screen width
            of at least 1024px. Please access this site from a desktop computer
            to play the game.
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
};
