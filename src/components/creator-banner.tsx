"use client";

import React from "react";
import { cn } from "@/lib/utils";

interface CreatorBannerProps {
  className?: string;
  variant?: "light" | "dark";
}

interface CreatorLinkProps {
  username: string;
  href: string;
  hoverTextColor: string;
}

const CreatorLink: React.FC<CreatorLinkProps> = ({
  username,
  href,
  hoverTextColor,
}) => (
  <a
    href={href}
    target="_blank"
    rel="noopener noreferrer"
    className={cn("mx-1 font-medium hover:underline", hoverTextColor)}
  >
    {username}
  </a>
);

export const CreatorBanner: React.FC<CreatorBannerProps> = ({
  className,
  variant = "dark",
}) => {
  const textColor = variant === "dark" ? "text-white/80" : "text-black/80";
  const hoverTextColor =
    variant === "dark" ? "hover:text-white" : "hover:text-black";

  const creators = [
    { username: "@theSouilos", href: "https://x.com/theSouilos" },
    { username: "@0xmrudenko", href: "https://x.com/0xmrudenko" },
  ];

  return (
    <div
      className={cn(
        "text-sm flex justify-center items-center py-2 w-[300px] ml-auto mt-auto",
        textColor,
        className
      )}
    >
      <span>Created by </span>
      {creators.map((creator, index) => (
        <React.Fragment key={creator.username}>
          <CreatorLink
            username={creator.username}
            href={creator.href}
            hoverTextColor={hoverTextColor}
          />
          {index < creators.length - 1 && <span>&</span>}
        </React.Fragment>
      ))}
    </div>
  );
};
